<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo $__env->yieldContent('title', 'เข้าสู่ระบบ - SoloShop'); ?></title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo e(asset('favicon.ico')); ?>">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@300;400;500;600;700&family=Noto+Sans+Thai:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #34495e;
            --accent-color: #e74c3c;
            --soft-gold: #d4af37;
            --warm-white: #fdfcf8;
            --soft-gray: #7f8c8d;
            --light-gray: #ecf0f1;
            --shadow-light: rgba(0, 0, 0, 0.06);
            --shadow-medium: rgba(0, 0, 0, 0.1);
            --shadow-heavy: rgba(0, 0, 0, 0.15);
            --gradient-start: #f8f9fa;
            --gradient-end: #e9ecef;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans Thai', 'Sarabun', sans-serif;
            background: linear-gradient(135deg, #f7f9fc 0%, #eef2f7 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            position: relative;
            margin: 0;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 177, 153, 0.08) 0%, transparent 50%);
            pointer-events: none;
            z-index: 1;
        }

        .auth-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.9);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
            position: relative;
            z-index: 2;
            animation: gentleFloat 0.6s ease-out;
        }

        @keyframes gentleFloat {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.98);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .auth-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 2rem 2rem;
            text-align: center;
            position: relative;
        }

        .auth-header h1 {
            margin: 0;
            font-size: 2rem;
            font-weight: 500;
            color: white;
            letter-spacing: -0.3px;
        }

        .auth-header p {
            margin: 0.5rem 0 0 0;
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.95rem;
            font-weight: 300;
        }

        .auth-body {
            padding: 2.5rem 2rem;
            background: white;
        }

        .form-floating {
            position: relative;
            margin-bottom: 2rem;
        }

        .form-control {
            border-radius: 12px;
            border: 1px solid #e1e5e9;
            padding: 1rem 1rem 1rem 3.5rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            background-color: #f8f9fa;
            height: 56px;
            position: relative;
            font-weight: 400;
            color: #495057;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.15);
            background-color: white;
            outline: none;
        }

        .form-control:not(:placeholder-shown) {
            background-color: white;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 1rem 2rem;
            font-weight: 500;
            font-size: 1rem;
            transition: all 0.3s ease;
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-1px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:active {
            transform: translateY(0);
        }

        .form-label {
            font-weight: 400;
            color: #6c757d;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            position: absolute;
            top: 1rem;
            left: 3.5rem;
            transition: all 0.3s ease;
            pointer-events: none;
            z-index: 1;
        }

        .form-control:focus + .form-label,
        .form-control:not(:placeholder-shown) + .form-label {
            top: -0.5rem;
            left: 1rem;
            font-size: 0.75rem;
            color: #667eea;
            background: white;
            padding: 0 0.5rem;
        }

        .auth-footer {
            text-align: center;
            padding: 2rem 2.5rem 2.5rem;
            color: var(--soft-gray);
            font-size: 0.875rem;
            background: linear-gradient(to bottom, var(--warm-white), rgba(236, 240, 241, 0.3));
        }

        .auth-footer a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .auth-footer a:hover {
            color: var(--soft-gold);
            text-decoration: underline;
        }

        .logo-container {
            margin-bottom: 2rem;
            position: relative;
            z-index: 1;
        }

        .logo-container i {
            font-size: 3rem;
            color: white;
            opacity: 0.9;
        }

        .alert {
            border-radius: 16px;
            margin-bottom: 2rem;
            border: none;
            animation: gentleSlideDown 0.5s ease-out;
        }

        @keyframes gentleSlideDown {
            from {
                opacity: 0;
                transform: translateY(-15px) scale(0.98);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .alert-danger {
            background: linear-gradient(135deg, var(--accent-color) 0%, #c0392b 100%);
            color: var(--warm-white);
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.25);
        }

        .alert-light {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(236, 240, 241, 0.8);
            color: var(--soft-gray);
            backdrop-filter: blur(10px);
        }

        .invalid-feedback {
            font-size: 0.875rem;
            margin-top: 0.4rem;
            color: var(--accent-color);
            font-weight: 500;
        }

        .form-check {
            margin: 2rem 0;
        }

        .form-check-input {
            width: 1.3rem;
            height: 1.3rem;
            border-radius: 8px;
            border: 2px solid var(--light-gray);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .form-check-input:checked {
            background-color: var(--soft-gold);
            border-color: var(--soft-gold);
            box-shadow: 0 4px 12px rgba(243, 156, 18, 0.3);
        }

        .form-check-label {
            color: var(--primary-color);
            font-size: 0.95rem;
            font-weight: 500;
            margin-left: 0.6rem;
            cursor: pointer;
        }

        .input-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            font-size: 1.1rem;
            z-index: 2;
            transition: color 0.3s ease;
        }

        .form-control:focus ~ .input-icon {
            color: #667eea;
        }

        .password-toggle {
            position: absolute;
            right: 1.2rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--soft-gray);
            cursor: pointer;
            z-index: 2;
            transition: color 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            padding: 0.5rem;
            border-radius: 8px;
        }

        .password-toggle:hover {
            color: var(--soft-gold);
            background: rgba(243, 156, 18, 0.1);
        }

        .btn-lg {
            padding: 1.2rem 2.5rem;
            font-size: 1.1rem;
            min-height: 64px;
        }

        .forgot-password {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }

        .forgot-password:hover {
            color: var(--soft-gold);
            text-decoration: underline;
        }

        .test-credentials {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1.2rem;
            margin-top: 1.5rem;
            border: 1px solid #e9ecef;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .test-credentials:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
            background: #ffffff;
        }

        .test-credentials .badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-size: 0.75rem;
            padding: 0.3rem 0.6rem;
            border-radius: 6px;
        }

        @media (max-width: 576px) {
            body {
                padding: 0.5rem;
            }

            .auth-container {
                margin: 0;
                border-radius: 20px;
                max-width: 100%;
            }

            .auth-header {
                padding: 2.5rem 1.5rem 2rem;
            }

            .auth-header h1 {
                font-size: 1.9rem;
            }

            .auth-body {
                padding: 2.5rem 1.5rem;
            }

            .form-control {
                padding: 1rem 1rem 1rem 3.5rem;
                height: 60px;
            }

            .btn-lg {
                padding: 1rem 2rem;
                min-height: 60px;
                font-size: 1rem;
            }

            .logo-container i {
                font-size: 3rem;
            }
        }

        /* Loading animation */
        .btn-loading {
            position: relative;
            color: transparent !important;
        }

        .btn-loading::after {
            content: '';
            position: absolute;
            width: 22px;
            height: 22px;
            top: 50%;
            left: 50%;
            margin-left: -11px;
            margin-top: -11px;
            border: 2px solid transparent;
            border-top-color: var(--warm-white);
            border-radius: 50%;
            animation: gentleSpin 1.2s linear infinite;
        }

        @keyframes gentleSpin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Smooth transitions for all interactive elements */
        * {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Focus outline for accessibility */
        .form-control:focus,
        .btn:focus,
        .form-check-input:focus {
            outline: 2px solid var(--soft-gold);
            outline-offset: 2px;
        }
    </style>

    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body>
    <div class="auth-container">
        <div class="auth-header">
            <div class="logo-container">
                <i class="fas fa-lotus"></i>
            </div>
            <h1>SoloShop</h1>
            <p>ระบบจัดการร้านค้า</p>
        </div>

        <div class="auth-body">
            <?php echo $__env->yieldContent('content'); ?>
        </div>

        <div class="auth-footer">
            <small>&copy; <?php echo e(date('Y')); ?> SoloShop - ด้วยความเคารพและเกียรติ</small>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Enhanced floating label functionality with smooth animations
        document.addEventListener('DOMContentLoaded', function() {
            const formControls = document.querySelectorAll('.form-control');

            formControls.forEach(function(input) {
                // Check if input has value on page load
                checkInputValue(input);

                // Add event listeners
                input.addEventListener('input', function() {
                    checkInputValue(this);
                });

                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                    addFocusEffect(this);
                });

                input.addEventListener('blur', function() {
                    this.parentElement.classList.remove('focused');
                    checkInputValue(this);
                });
            });

            function checkInputValue(input) {
                const label = input.nextElementSibling;
                if (input.value.trim() !== '' || input === document.activeElement) {
                    if (label && label.classList.contains('form-label')) {
                        label.style.top = '-0.6rem';
                        label.style.left = '1.2rem';
                        label.style.fontSize = '0.8rem';
                        label.style.color = 'var(--soft-gold)';
                        label.style.background = 'var(--warm-white)';
                        label.style.padding = '0 0.6rem';
                    }
                } else {
                    if (label && label.classList.contains('form-label')) {
                        label.style.top = '1.2rem';
                        label.style.left = '3.8rem';
                        label.style.fontSize = '0.95rem';
                        label.style.color = 'var(--soft-gray)';
                        label.style.background = 'transparent';
                        label.style.padding = '0';
                    }
                }
            }

            function addFocusEffect(input) {
                // Add subtle ripple effect
                const ripple = document.createElement('div');
                ripple.style.position = 'absolute';
                ripple.style.borderRadius = '50%';
                ripple.style.background = 'rgba(243, 156, 18, 0.3)';
                ripple.style.transform = 'scale(0)';
                ripple.style.animation = 'ripple 0.6s linear';
                ripple.style.left = '50%';
                ripple.style.top = '50%';
                ripple.style.width = '20px';
                ripple.style.height = '20px';
                ripple.style.marginLeft = '-10px';
                ripple.style.marginTop = '-10px';

                input.parentElement.style.position = 'relative';
                input.parentElement.appendChild(ripple);

                setTimeout(() => {
                    ripple.remove();
                }, 600);
            }

            // Add smooth page entrance
            document.body.style.opacity = '0';
            setTimeout(function() {
                document.body.style.transition = 'opacity 0.5s ease';
                document.body.style.opacity = '1';
            }, 150);
        });

        // Enhanced keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                const focusedElement = document.activeElement;
                if (focusedElement.type === 'email') {
                    document.getElementById('password').focus();
                    e.preventDefault();
                } else if (focusedElement.type === 'password') {
                    document.getElementById('loginBtn').click();
                    e.preventDefault();
                }
            }
        });

        // Add CSS for ripple animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/layouts/auth.blade.php ENDPATH**/ ?>