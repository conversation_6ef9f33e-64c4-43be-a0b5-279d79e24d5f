<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'เข้าสู่ระบบ - SoloShop')</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@300;400;500;600;700&family=Noto+Sans+Thai:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #34495e;
            --accent-color: #e74c3c;
            --soft-gold: #d4af37;
            --warm-white: #fdfcf8;
            --soft-gray: #7f8c8d;
            --light-gray: #ecf0f1;
            --shadow-light: rgba(0, 0, 0, 0.06);
            --shadow-medium: rgba(0, 0, 0, 0.1);
            --shadow-heavy: rgba(0, 0, 0, 0.15);
            --gradient-start: #f8f9fa;
            --gradient-end: #e9ecef;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans Thai', 'Sarabun', sans-serif;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            position: relative;
            overflow-x: hidden;
            margin: 0;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 25% 75%, rgba(212, 175, 55, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 75% 25%, rgba(44, 62, 80, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(127, 140, 141, 0.08) 0%, transparent 50%);
            pointer-events: none;
            z-index: 1;
        }

        body::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(212,175,55,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
            pointer-events: none;
            z-index: 1;
            opacity: 0.3;
        }

        .auth-container {
            background: var(--warm-white);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow:
                0 25px 50px var(--shadow-light),
                0 15px 35px var(--shadow-medium),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.8);
            overflow: hidden;
            max-width: 420px;
            width: 100%;
            position: relative;
            z-index: 2;
            animation: gentleFloat 0.8s ease-out;
        }

        @keyframes gentleFloat {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.98);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .auth-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: var(--warm-white);
            padding: 3.5rem 2rem 2.5rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .auth-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.05) 0%, transparent 70%);
            animation: gentleRotate 30s linear infinite;
        }

        @keyframes gentleRotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .auth-header h1 {
            margin: 0;
            font-size: 2.2rem;
            font-weight: 600;
            color: var(--warm-white);
            position: relative;
            z-index: 1;
            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            letter-spacing: -0.5px;
        }

        .auth-header p {
            margin: 0.8rem 0 0 0;
            color: rgba(253, 252, 248, 0.85);
            font-size: 1rem;
            font-weight: 400;
            position: relative;
            z-index: 1;
            letter-spacing: 0.3px;
        }

        .auth-body {
            padding: 3rem 2.5rem;
            background: var(--warm-white);
        }

        .form-floating {
            position: relative;
            margin-bottom: 2rem;
        }

        .form-control {
            border-radius: 16px;
            border: 2px solid var(--light-gray);
            padding: 1.2rem 1.2rem 1.2rem 3.8rem;
            font-size: 1rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            background-color: rgba(255, 255, 255, 0.8);
            height: 64px;
            position: relative;
            font-weight: 400;
            color: var(--primary-color);
        }

        .form-control:focus {
            border-color: var(--soft-gold);
            box-shadow: 0 0 0 0.25rem rgba(243, 156, 18, 0.1);
            background-color: white;
            transform: translateY(-1px);
            outline: none;
        }

        .form-control:not(:placeholder-shown) {
            background-color: white;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            border-radius: 16px;
            padding: 1.2rem 2.5rem;
            font-weight: 500;
            font-size: 1.1rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            letter-spacing: 0.3px;
            color: var(--warm-white);
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.15), transparent);
            transition: left 0.6s ease;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            transform: translateY(-2px);
            box-shadow: 0 15px 30px var(--shadow-medium);
        }

        .btn-primary:active {
            transform: translateY(0);
        }

        .form-label {
            font-weight: 500;
            color: var(--soft-gray);
            margin-bottom: 0.5rem;
            font-size: 0.95rem;
            position: absolute;
            top: 1.2rem;
            left: 3.8rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            pointer-events: none;
            z-index: 1;
        }

        .form-control:focus + .form-label,
        .form-control:not(:placeholder-shown) + .form-label {
            top: -0.6rem;
            left: 1.2rem;
            font-size: 0.8rem;
            color: var(--soft-gold);
            background: var(--warm-white);
            padding: 0 0.6rem;
        }

        .auth-footer {
            text-align: center;
            padding: 2rem 2.5rem 2.5rem;
            color: var(--soft-gray);
            font-size: 0.875rem;
            background: linear-gradient(to bottom, var(--warm-white), rgba(236, 240, 241, 0.3));
        }

        .auth-footer a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .auth-footer a:hover {
            color: var(--soft-gold);
            text-decoration: underline;
        }

        .logo-container {
            margin-bottom: 2rem;
            position: relative;
            z-index: 1;
        }

        .logo-container i {
            font-size: 3.5rem;
            color: var(--warm-white);
            text-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            animation: gentlePulse 4s ease-in-out infinite;
            filter: drop-shadow(0 0 10px rgba(212, 175, 55, 0.3));
        }

        @keyframes gentlePulse {
            0%, 100% {
                transform: scale(1) rotate(0deg);
                filter: drop-shadow(0 0 10px rgba(212, 175, 55, 0.3));
            }
            50% {
                transform: scale(1.05) rotate(2deg);
                filter: drop-shadow(0 0 15px rgba(212, 175, 55, 0.5));
            }
        }

        .alert {
            border-radius: 16px;
            margin-bottom: 2rem;
            border: none;
            animation: gentleSlideDown 0.5s ease-out;
        }

        @keyframes gentleSlideDown {
            from {
                opacity: 0;
                transform: translateY(-15px) scale(0.98);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .alert-danger {
            background: linear-gradient(135deg, var(--accent-color) 0%, #c0392b 100%);
            color: var(--warm-white);
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.25);
        }

        .alert-light {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(236, 240, 241, 0.8);
            color: var(--soft-gray);
            backdrop-filter: blur(10px);
        }

        .invalid-feedback {
            font-size: 0.875rem;
            margin-top: 0.4rem;
            color: var(--accent-color);
            font-weight: 500;
        }

        .form-check {
            margin: 2rem 0;
        }

        .form-check-input {
            width: 1.3rem;
            height: 1.3rem;
            border-radius: 8px;
            border: 2px solid var(--light-gray);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .form-check-input:checked {
            background-color: var(--soft-gold);
            border-color: var(--soft-gold);
            box-shadow: 0 4px 12px rgba(243, 156, 18, 0.3);
        }

        .form-check-label {
            color: var(--primary-color);
            font-size: 0.95rem;
            font-weight: 500;
            margin-left: 0.6rem;
            cursor: pointer;
        }

        .input-icon {
            position: absolute;
            left: 1.2rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--soft-gray);
            font-size: 1.2rem;
            z-index: 2;
            transition: color 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .form-control:focus ~ .input-icon {
            color: var(--soft-gold);
        }

        .password-toggle {
            position: absolute;
            right: 1.2rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--soft-gray);
            cursor: pointer;
            z-index: 2;
            transition: color 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            padding: 0.5rem;
            border-radius: 8px;
        }

        .password-toggle:hover {
            color: var(--soft-gold);
            background: rgba(243, 156, 18, 0.1);
        }

        .btn-lg {
            padding: 1.2rem 2.5rem;
            font-size: 1.1rem;
            min-height: 64px;
        }

        .forgot-password {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }

        .forgot-password:hover {
            color: var(--soft-gold);
            text-decoration: underline;
        }

        .test-credentials {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(236, 240, 241, 0.6) 100%);
            border-radius: 16px;
            padding: 1.5rem;
            margin-top: 2rem;
            border: 1px solid rgba(236, 240, 241, 0.8);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .test-credentials:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 25px var(--shadow-medium);
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(212, 175, 55, 0.1) 100%);
        }

        .test-credentials .badge {
            background: linear-gradient(135deg, var(--soft-gold) 0%, #e67e22 100%);
            color: var(--warm-white);
            font-size: 0.8rem;
            padding: 0.4rem 0.8rem;
            border-radius: 8px;
        }

        @media (max-width: 576px) {
            body {
                padding: 0.5rem;
            }

            .auth-container {
                margin: 0;
                border-radius: 20px;
                max-width: 100%;
            }

            .auth-header {
                padding: 2.5rem 1.5rem 2rem;
            }

            .auth-header h1 {
                font-size: 1.9rem;
            }

            .auth-body {
                padding: 2.5rem 1.5rem;
            }

            .form-control {
                padding: 1rem 1rem 1rem 3.5rem;
                height: 60px;
            }

            .btn-lg {
                padding: 1rem 2rem;
                min-height: 60px;
                font-size: 1rem;
            }

            .logo-container i {
                font-size: 3rem;
            }
        }

        /* Loading animation */
        .btn-loading {
            position: relative;
            color: transparent !important;
        }

        .btn-loading::after {
            content: '';
            position: absolute;
            width: 22px;
            height: 22px;
            top: 50%;
            left: 50%;
            margin-left: -11px;
            margin-top: -11px;
            border: 2px solid transparent;
            border-top-color: var(--warm-white);
            border-radius: 50%;
            animation: gentleSpin 1.2s linear infinite;
        }

        @keyframes gentleSpin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Smooth transitions for all interactive elements */
        * {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Focus outline for accessibility */
        .form-control:focus,
        .btn:focus,
        .form-check-input:focus {
            outline: 2px solid var(--soft-gold);
            outline-offset: 2px;
        }
    </style>

    @stack('styles')
</head>
<body>
    <div class="auth-container">
        <div class="auth-header">
            <div class="logo-container">
                <i class="fas fa-lotus"></i>
            </div>
            <h1>SoloShop</h1>
            <p>ระบบจัดการร้านค้า</p>
        </div>

        <div class="auth-body">
            @yield('content')
        </div>

        <div class="auth-footer">
            <small>&copy; {{ date('Y') }} SoloShop - ด้วยความเคารพและเกียรติ</small>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Enhanced floating label functionality with smooth animations
        document.addEventListener('DOMContentLoaded', function() {
            const formControls = document.querySelectorAll('.form-control');

            formControls.forEach(function(input) {
                // Check if input has value on page load
                checkInputValue(input);

                // Add event listeners
                input.addEventListener('input', function() {
                    checkInputValue(this);
                });

                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                    addFocusEffect(this);
                });

                input.addEventListener('blur', function() {
                    this.parentElement.classList.remove('focused');
                    checkInputValue(this);
                });
            });

            function checkInputValue(input) {
                const label = input.nextElementSibling;
                if (input.value.trim() !== '' || input === document.activeElement) {
                    if (label && label.classList.contains('form-label')) {
                        label.style.top = '-0.6rem';
                        label.style.left = '1.2rem';
                        label.style.fontSize = '0.8rem';
                        label.style.color = 'var(--soft-gold)';
                        label.style.background = 'var(--warm-white)';
                        label.style.padding = '0 0.6rem';
                    }
                } else {
                    if (label && label.classList.contains('form-label')) {
                        label.style.top = '1.2rem';
                        label.style.left = '3.8rem';
                        label.style.fontSize = '0.95rem';
                        label.style.color = 'var(--soft-gray)';
                        label.style.background = 'transparent';
                        label.style.padding = '0';
                    }
                }
            }

            function addFocusEffect(input) {
                // Add subtle ripple effect
                const ripple = document.createElement('div');
                ripple.style.position = 'absolute';
                ripple.style.borderRadius = '50%';
                ripple.style.background = 'rgba(243, 156, 18, 0.3)';
                ripple.style.transform = 'scale(0)';
                ripple.style.animation = 'ripple 0.6s linear';
                ripple.style.left = '50%';
                ripple.style.top = '50%';
                ripple.style.width = '20px';
                ripple.style.height = '20px';
                ripple.style.marginLeft = '-10px';
                ripple.style.marginTop = '-10px';

                input.parentElement.style.position = 'relative';
                input.parentElement.appendChild(ripple);

                setTimeout(() => {
                    ripple.remove();
                }, 600);
            }

            // Add smooth page entrance
            document.body.style.opacity = '0';
            setTimeout(function() {
                document.body.style.transition = 'opacity 0.5s ease';
                document.body.style.opacity = '1';
            }, 150);
        });

        // Enhanced keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                const focusedElement = document.activeElement;
                if (focusedElement.type === 'email') {
                    document.getElementById('password').focus();
                    e.preventDefault();
                } else if (focusedElement.type === 'password') {
                    document.getElementById('loginBtn').click();
                    e.preventDefault();
                }
            }
        });

        // Add CSS for ripple animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>

    @stack('scripts')
</body>
</html>
